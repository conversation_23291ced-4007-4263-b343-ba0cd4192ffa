{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@formspree/react": "^3.0.0", "@react-three/drei": "^10.5.1", "@react-three/fiber": "^9.2.0", "@tailwindcss/vite": "^4.1.11", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.7.0", "react-tsparticles": "^2.12.2", "tailwindcss": "^4.1.11", "three": "^0.178.0", "tsparticles-slim": "^2.12.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4"}}