import React from "react";
import { motion } from "framer-motion";
import { Quote, Mail, Smartphone } from "lucide-react";
import karthik from "../assets/Karthik.jpg";
import jogeeswara from "../assets/P.Jogeeswara V.N.S.jpg";

const cofounders = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Co-Founder",
    avatar: karthik,
    rating: 5,
    email: " <EMAIL>",
    phone: "+91 8008085533",
  },
  {
    id: 2,
    name: "P.Jogeeswara V.N.S",
    role: "Co-Founder",
    avatar: jogees<PERSON>,
    rating: 5,
    email: "<EMAIL>",
    phone: "+91 9063316737",
  },
];

const Testimonials = () => {
  return (
    <div className="w-full bg-[#110F15] py-10 px-4 sm:px-6 lg:px-10">
      {/* Section Title */}
      <motion.h2
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white text-center mb-8 sm:mb-12"
      >
        Our <span className="text-[#9b5de5]">Founders</span>
      </motion.h2>

      {/* Founders Grid */}
      <div className="max-w-7xl mx-auto grid gap-8 sm:gap-12 sm:grid-cols-2 px-4">
        {cofounders.map(
          ({ id, name, role, content, avatar, rating, email, phone }) => (
            <motion.div
              key={id}
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: id * 0.2 }}
              className="relative rounded-2xl p-6 sm:p-8 lg:p-10 bg-[#181924] border border-[#1f1f2e] shadow-lg hover:border-[#9b5de5] hover:shadow-[0_0_15px_#9b5de5] transition-all duration-300"
            >
              {/* Decorative blurred circles */}
              <div className="absolute top-5 right-5 w-24 h-24 rounded-full bg-[#9b5de5] opacity-10 filter blur-xl pointer-events-none"></div>
              <div className="absolute bottom-6 left-6 w-28 h-28 rounded-full bg-[#9b5de5] opacity-5 filter blur-2xl pointer-events-none"></div>
              {/* Quote Icon */}
              <Quote
                size={36}
                className="absolute top-5 left-5 text-[#9b5de5] opacity-20 pointer-events-none"
              />

              {/* Founder Info */}
              <div className="flex flex-col items-center text-center relative z-10">
                {/* Avatar with solid violet border and background */}
                {/* Avatar photo */}
                <img
                  src={avatar}
                  alt={`${name}'s avatar`}
                  className="w-20 sm:w-24 h-20 sm:h-24 mb-4 sm:mb-6 rounded-full object-cover border-4 border-[#9b5de5] bg-[#181924]"
                />

                {/* Name & Role */}
                <h3 className="text-xl sm:text-2xl font-bold text-white">{name}</h3>
                <p className="text-[#9b5de5] font-medium mb-3">{role}</p>

                {/* Contact Info */}
                <div className="flex flex-col gap-2 mb-4 text-gray-300 text-xs sm:text-sm">
                  <div className="flex items-center justify-center space-x-2">
                    <Mail size={16} className="text-[#9b5de5]" />
                    <a
                      href={`mailto:${email}`}
                      className="hover:text-[#f15bb5] transition-colors"
                    >
                      {email}
                    </a>
                  </div>
                  <div className="flex items-center justify-center space-x-2">
                    <Smartphone size={16} className="text-[#9b5de5]" />
                    <a
                      href={`tel:${phone.replace(/\s+/g, "")}`}
                      className="hover:text-[#f15bb5] transition-colors"
                    >
                      {phone}
                    </a>
                  </div>
                </div>
              </div>
            </motion.div>
          )
        )}
      </div>
    </div>
  );
};

export default Testimonials;
