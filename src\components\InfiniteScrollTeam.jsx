import React, { useEffect, useRef, useState } from 'react';
import shiva from "../assets/shiva.jpeg";
import ritheesh from "../assets/ritheesh.jpg";
import praneeth from "../assets/praneeth.jpg";
import ramnagendra from "../assets/ramnagendra.jpeg";
import harin from "../assets/harin.jpg"

// Simple icon components
const Mail = ({ size, className }) => (
  <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
    <polyline points="22,6 12,13 2,6"/>
  </svg>
);

const Phone = ({ size, className }) => (
  <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 1 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 1 2.81.7A2 2 0 0 1 22 16.92z"/>
  </svg>
);

const teamMembers = [
  {
    id: 1,
    name: "Shiva Ram Kumar Jella",
    role: "AI-Developer Intern",
    avatar: shiva,
    email: "<EMAIL>",
    phone: "+91 9908922795",
  },
  {
    id: 2,
    name: "S.Naga Syam Praneeth",
    role: "AI-Developer Intern",
    avatar: praneeth,
    email: "<EMAIL>",
    phone: "+91 9346521315",
  },
  {
    id: 3,
    name: "S.Ramnagendra Varma",
    role: "AI-Developer Intern",
    avatar: ramnagendra,
    email: "<EMAIL>",
    phone: "+91 9533376668",
  },
  {
    id: 4,
    name: "Ritheesh Reddy Andem",
    role: "Web-Developer Intern",
    avatar: ritheesh,
    email: "<EMAIL>",
    phone: "+91 6305306381",
  },
  {
    id:5,
    name: "Harin Guptha Pasumarthi",
    role:"Web-Developer Intern",
    avatar: harin,
    email: "<EMAIL>",
    phone: "+91 8897410780" }
];

const InfiniteScrollTeam = () => {
  const scrollRef = useRef(null);
  const animationRef = useRef(null);
  const [paused, setPaused] = useState(false);

  useEffect(() => {
    const scrollContainer = scrollRef.current;
    if (!scrollContainer) return;

    const speed = 1.5; // pixels per frame
    const cardWidth = 380; // approximate width including gap
    const totalWidth = cardWidth * teamMembers.length;

    const animateScroll = () => {
      if (!paused && scrollContainer) {
        // Get current scroll position
        const currentScroll = scrollContainer.scrollLeft;
        
        // If we've scrolled past the first set, reset to beginning
        if (currentScroll >= totalWidth) {
          scrollContainer.scrollLeft = 0;
        } else {
          scrollContainer.scrollLeft = currentScroll + speed;
        }
      }
      animationRef.current = requestAnimationFrame(animateScroll);
    };

    animationRef.current = requestAnimationFrame(animateScroll);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [paused]);

  const handleMouseEnter = () => setPaused(true);
  const handleMouseLeave = () => setPaused(false);
  const handleTouchStart = () => setPaused(true);
  const handleTouchEnd = () => setPaused(false);

  return (
    <div id="team" className="w-full scroll-mt-24 bg-[#110F15] pt-16 sm:pt-20 mb-25 pb-16 sm:pb-20">
      <style>
        {`
          .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
          }
          .scrollbar-hide::-webkit-scrollbar {
            display: none;
          }
          .team-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          }
          .team-card:hover {
            transform: scale(1.02);
            box-shadow: 0 0 20px rgba(155, 93, 229, 0.4);
          }
          .team-avatar {
            transition: transform 0.2s ease;
          }
          .team-card:hover .team-avatar {
            transform: scale(1.05);
          }
        `}
      </style>

      <div className="px-4 sm:px-6 lg:px-10">
        <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white text-center mb-8 sm:mb-12">
          Meet Our <span className="text-[#9b5de5]">Team</span>
        </h2>
      </div>

      <div
        ref={scrollRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        className="flex gap-6 px-6 py-4 overflow-x-hidden scrollbar-hide"
        style={{ 
          scrollBehavior: 'auto',
          WebkitOverflowScrolling: 'touch'
        }}
      >
        {/* Render multiple sets for seamless infinite scroll */}
        {[...Array(3)].map((_, setIndex) => 
          teamMembers.map((member, index) => (
            <div
              key={`${setIndex}-${index}`}
              className="min-w-[280px] sm:min-w-[320px] md:min-w-[380px] lg:min-w-[420px] min-h-[300px] sm:min-h-[320px] flex-shrink-0 border border-[#1f1f2e] hover:border-[#9b5de5] rounded-2xl p-6 sm:p-8 lg:p-10 bg-[#181924] text-white team-card cursor-pointer"
            >
              <div className="flex flex-col items-center text-center">
                <div className="mb-4 team-avatar">
                  <img
                    src={member.avatar}
                    alt={member.name}
                    className="w-16 sm:w-20 h-16 sm:h-20 rounded-full object-cover border-2 border-[#9b5de5]"
                  />
                </div>
                <h3 className="text-lg sm:text-xl font-semibold mb-2">{member.name}</h3>
                <p className="text-[#9b5de5] font-medium mb-4 text-sm sm:text-base">{member.role}</p>
                
                <div className="space-y-2 w-full">
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-300">
                    <Mail size={16} />
                    <span className="truncate">{member.email}</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-300">
                    <Phone size={16} />
                    <span>{member.phone}</span>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default InfiniteScrollTeam;