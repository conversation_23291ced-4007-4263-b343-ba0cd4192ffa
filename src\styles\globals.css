@import "tailwindcss/preflight";
/* @tailwind components; */
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', sans-serif;
  color: white;
  overflow-x: hidden;
  background: #181924;
  min-width: 320px;
}

/* Main background with subtle gradient */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at 20% 30%,
    rgba(63, 13, 96, 0.15) 0%,
    rgba(24, 25, 36, 1) 70%
  );
  z-index: -2;
}

/* Content container to ensure readability */
.content-container {
  position: relative;
  z-index: 2;
  background: rgba(24, 25, 36, 0.7);
  backdrop-filter: blur(4px);
}

* {
  box-sizing: border-box;
}

img {
  max-width: 100%;
  height: auto;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #2e2e38;
}

::-webkit-scrollbar-thumb {
  background: #9b5de5;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #7d3fd1;
}

/* Section styling */
.section {
  position: relative;
  padding: 2.5rem 0;
}

.section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(38, 39, 48, 0.4);
  backdrop-filter: blur(2px);
  z-index: -1;
}

/* Card styling */
.card {
  background: rgba(38, 39, 48, 0.6);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(155, 93, 229, 0.1);
  border-radius: 1rem;
  transition: all 0.3s ease;
}

.card:hover {
  border-color: rgba(155, 93, 229, 0.3);
  box-shadow: 0 10px 25px -5px rgba(155, 93, 229, 0.2);
}

/* Text contrast improvements */
.text-primary {
  color: #ffffff;
}

.text-secondary {
  color: #e0e0e0;
}

.text-accent {
  color: #9b5de5;
}

/* Button styling */
.btn-primary {
  background: #9b5de5;
  color: white;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: #7d3fd1;
  transform: translateY(-2px);
  box-shadow: 0 10px 20px -5px rgba(155, 93, 229, 0.4);
}

/* Input fields */
.input-field {
  background: rgba(38, 39, 48, 0.6);
  border: 1px solid rgba(155, 93, 229, 0.2);
  color: white;
  transition: all 0.3s ease;
}

.input-field:focus {
  border-color: #9b5de5;
  box-shadow: 0 0 0 3px rgba(155, 93, 229, 0.2);
}

/* Simplified animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.slide-up {
  animation: slideUp 0.6s ease-out forwards;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .section {
    padding: 3rem 0;
  }
  
  .section::before {
    backdrop-filter: blur(1px);
  }
}