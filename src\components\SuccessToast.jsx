import { motion } from 'framer-motion';
import { FiCheckCircle } from 'react-icons/fi';

const SuccessToast = ({ setShowToast }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="fixed bottom-4 sm:bottom-8 right-4 sm:right-8 left-4 sm:left-auto bg-[#2e2e38] border border-[#3d3d47] rounded-lg p-3 sm:p-4 shadow-lg z-50 flex items-center max-w-sm sm:max-w-none"
    >
      <div className="mr-3 text-green-500 flex-shrink-0">
        <FiCheckCircle size={20} className="sm:w-6 sm:h-6" />
      </div>
      <div className="flex-1 min-w-0">
        <h4 className="font-bold text-sm sm:text-base">Message Sent!</h4>
        <p className="text-xs sm:text-sm text-[#c9c9c9]">We'll get back to you soon.</p>
      </div>
      <button
        onClick={() => setShowToast(false)}
        className="ml-3 sm:ml-4 text-[#a0a0a0] hover:text-white flex-shrink-0"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>
    </motion.div>
  );
};

export default SuccessToast;